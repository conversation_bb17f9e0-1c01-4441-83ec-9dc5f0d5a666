import 'package:flutter/material.dart';

void main() => runApp(const DriveOnApp());

class DriveOnApp extends StatelessWidget {
  const DriveOnApp({super.key});

  @override
  Widget build(BuildContext context) {
    const yellow = Color(0xFFFFC107); // Amber/Yellow accent
    return MaterialApp(
      title: 'Drive-On',
      debugShowCheckedModeBanner: false, // remove the debug banner
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
          seedColor: yellow,
          brightness: Brightness.dark,
        ),
        scaffoldBackgroundColor: const Color(0xFF0D0E10),
        textTheme: const TextTheme(
          displaySmall: TextStyle(fontSize: 40, fontWeight: FontWeight.w800),
          titleMedium: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
          bodyMedium: TextStyle(fontSize: 14, height: 1.4),
        ),
      ),
      home: const LoginScreen(),
    );
  }
}

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _fadeIn;
  bool _pulse = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 1200));
    _fadeIn = CurvedAnimation(parent: _controller, curve: Curves.easeOut);
    _controller.forward();
    // subtle pulse for the floating dots
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) setState(() => _pulse = true);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final yellow = Theme.of(context).colorScheme.secondary;
    return Scaffold(
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Color(0xFF0F1113), Color(0xFF0A0B0C)],
              ),
            ),
          ),
          // Soft vignette and floating dots
          Positioned.fill(
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 1000),
              opacity: _fadeIn.value,
              child: Stack(children: const [
                _Dot(top: 80, left: 28, size: 6),
                _Dot(top: 150, right: 36, size: 4),
                _Dot(bottom: 120, left: 48, size: 5),
                _Dot(bottom: 80, right: 36, size: 6),
              ]),
            ),
          ),

          // Main content
          SafeArea(
            child: Center(
              child: FadeTransition(
                opacity: _fadeIn,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 28.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // App "logo"
                      Text(
                        'Drive-On',
                        style: Theme.of(context)
                            .textTheme
                            .displaySmall!
                            .copyWith(color: yellow),
                      ),
                      const SizedBox(height: 10),
                      Text('Welcome Back', style: Theme.of(context).textTheme.titleMedium!.copyWith(color: yellow)),
                      const SizedBox(height: 8),
                      Text('Sign in to continue your journey',
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.white70)),
                      const SizedBox(height: 36),

                      // Google button (no backend, just lets the user in)
                      _GoogleButton(
                        onPressed: () {
                          Navigator.of(context).pushReplacement(
                            MaterialPageRoute(builder: (_) => const HomeScreen()),
                          );
                        },
                        pulse: _pulse,
                      ),

                      const SizedBox(height: 36),
                      Opacity(
                        opacity: 0.8,
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: const TextSpan(
                            style: TextStyle(color: Colors.white70),
                            children: [
                              TextSpan(text: 'By continuing, you agree to our\n'),
                              TextSpan(text: 'Terms of Service'),
                              TextSpan(text: '  •  '),
                              TextSpan(text: 'Privacy Policy'),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _Dot extends StatefulWidget {
  final double? top, left, right, bottom, size;
  const _Dot({this.top, this.left, this.right, this.bottom, this.size,});

  @override
  State<_Dot> createState() => _DotState();
}

class _DotState extends State<_Dot> with SingleTickerProviderStateMixin {
  late final AnimationController _c = AnimationController(vsync: this, duration: const Duration(seconds: 3))..repeat(reverse: true);
  @override
  void dispose() { _c.dispose(); super.dispose(); }
  @override
  Widget build(BuildContext context) {
    final yellow = Theme.of(context).colorScheme.secondary;
    return AnimatedBuilder(
      animation: _c,
      builder: (_, __) {
        final offset = (widget.size ?? 6) / 2 * (_c.value - 0.5);
        return Positioned(
          top: widget.top != null ? widget.top! + offset : null,
          left: widget.left != null ? widget.left! + offset : null,
          right: widget.right != null ? widget.right! - offset : null,
          bottom: widget.bottom != null ? widget.bottom! - offset : null,
          child: Container(
            width: widget.size ?? 6,
            height: widget.size ?? 6,
            decoration: BoxDecoration(color: yellow, shape: BoxShape.circle, boxShadow: [
              BoxShadow(color: yellow.withOpacity(0.5), blurRadius: 6, spreadRadius: 0.5),
            ]),
          ),
        );
      },
    );
  }
}

class _GoogleButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool pulse;
  const _GoogleButton({required this.onPressed, required this.pulse});

  @override
  Widget build(BuildContext context) {
    final yellow = Theme.of(context).colorScheme.secondary;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeInOut,
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(color: yellow.withOpacity(pulse ? 0.35 : 0.15), blurRadius: pulse ? 18 : 8),
        ],
      ),
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: yellow, width: 2),
          foregroundColor: Colors.white,
          backgroundColor: const Color(0xFF121316),
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
        onPressed: onPressed,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 28,
              height: 28,
              decoration: const BoxDecoration(color: Colors.white, shape: BoxShape.circle),
              alignment: Alignment.center,
              child: const Text('G', style: TextStyle(color: Colors.black87, fontWeight: FontWeight.w700)),
            ),
            const SizedBox(width: 12),
            const Text('Continue with Google', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
            const SizedBox(width: 12),
            Icon(Icons.chevron_right, color: yellow),
          ],
        ),
      ),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});
  @override
  Widget build(BuildContext context) {
    final yellow = Theme.of(context).colorScheme.secondary;
    return Scaffold(
      appBar: AppBar(title: const Text('Drive-On')),
      body: Center(
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          Icon(Icons.directions_car_filled_rounded, color: yellow, size: 64),
          const SizedBox(height: 12),
          const Text("You're in!", style: TextStyle(fontSize: 20)),
        ]),
      ),
    );
  }
}
