import 'package:flutter_test/flutter_test.dart';
import 'package:d_o/main.dart';

void main() {
  testWidgets('Login screen renders and lets user in on Google tap', (tester) async {
    await tester.pumpWidget(const DriveOnApp());

    // Wait for animations to settle (including the 800ms timer)
    await tester.pump(const Duration(milliseconds: 900));

    // Verify login screen content
    expect(find.text('Drive-On'), findsWidgets);
    expect(find.text('Continue with Google'), findsOneWidget);

    // Tap the Google button and ensure we navigate to HomeScreen
    await tester.tap(find.text('Continue with Google'));
    await tester.pumpAndSettle();

    expect(find.text("You're in!"), findsOneWidget);
  });
}
